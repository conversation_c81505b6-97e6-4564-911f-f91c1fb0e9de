
<?php $__env->startSection('TitlePage', 'Order Details'); ?>
<?php $__env->startSection('content'); ?>
<div class="content">
    <div class="page-header">
        <div class="page-title">
            <h4>Order Details</h4>
            <h6>View order details</h6>
        </div>
    </div>
    <div class="card">
        <div class="card-body">
            <div class="card-sales-split">
                <h2>Order Detail : #<?php echo e($order->id); ?></h2>
                <ul>
                    <li>
                        <a data-bs-toggle="tooltip" data-bs-placement="top" title="pdf"><img
                                src="<?php echo e(asset('admin/assets/img/icons/pdf.svg')); ?>" alt="img"></a>
                    </li>
                    <li>
                        <a data-bs-toggle="tooltip" data-bs-placement="top" title="excel"><img
                                src="<?php echo e(asset('admin/assets/img/icons/excel.svg')); ?>" alt="img"></a>
                    </li>
                    <li>
                        <a data-bs-toggle="tooltip" data-bs-placement="top" title="print"><img
                                src="<?php echo e(asset('admin/assets/img/icons/printer.svg')); ?>" alt="img"></a>
                    </li>
                </ul>
            </div>
            <div class="invoice-box table-height"
                style="max-width: 1600px;width:100%;overflow: auto;margin:15px auto;padding: 0;font-size: 14px;line-height: 24px;color: #555;">
                <table cellpadding="0" cellspacing="0"
                    style="width: 100%;line-height: inherit;text-align: left;">
                    <tbody>
                        <tr class="top">
                            <td colspan="4" style="padding: 5px;vertical-align: top;">
                                <table style="width: 100%;line-height: inherit;text-align: left;">
                                    <tbody>
                                        <tr>
                                            <td
                                                style="padding:5px;vertical-align:top;text-align:left;padding-bottom:20px">
                                                <font style="vertical-align: inherit;margin-bottom:25px;">
                                                    <font
                                                        style="vertical-align: inherit;font-size:14px;color:#7367F0;font-weight:600;line-height: 35px; ">
                                                        Customer Info</font>
                                                </font><br>
                                                <font style="vertical-align: inherit;">
                                                    <font
                                                        style="vertical-align: inherit;font-size: 14px;color:#000;font-weight: 400;">
                                                        <?php echo e($order->name); ?></font>
                                                </font><br>
                                                <font style="vertical-align: inherit;">
                                                    <font
                                                        style="vertical-align: inherit;font-size: 14px;color:#000;font-weight: 400;">
                                                        Email : <?php echo e($order->email); ?>

                                                    </font>
                                                </font><br>
                                                <font style="vertical-align: inherit;">
                                                    <font
                                                        style="vertical-align: inherit;font-size: 14px;color:#000;font-weight: 400;">
                                                        Phone : <?php echo e($order->mobile); ?></font>
                                                </font><br>
                                                <font style="vertical-align: inherit;">
                                                    <font
                                                        style="vertical-align: inherit;font-size: 14px;color:#000;font-weight: 400;">
                                                        <?php echo e($order->address); ?></font>
                                                </font><br>
                                                <font
                                                        style="vertical-align: inherit;font-size: 14px;color:#000;font-weight: 400;">
                                                        <?php echo e($order->city); ?> , <?php echo e($order->countryName); ?> <?php echo e($order->zip); ?></font>
                                                </font><br>
                                            </td>
                                            <td
                                                style="padding:5px;vertical-align:top;text-align:left;padding-bottom:20px">
                                                <font style="vertical-align: inherit;margin-bottom:25px;">
                                                    <font
                                                        style="vertical-align: inherit;font-size:14px;color:#7367F0;font-weight:600;line-height: 35px; ">
                                                        Invoice Info</font>
                                                </font><br>
                                                <font style="vertical-align: inherit;">
                                                    <font
                                                        style="vertical-align: inherit;font-size: 14px;color:#000;font-weight: 400;">
                                                        Reference :</font>
                                                </font><br>
                                                <font style="vertical-align: inherit;">
                                                    <font
                                                        style="vertical-align: inherit;font-size: 14px;color:#000;font-weight: 400;">
                                                        Payment Status :</font>
                                                </font><br>
                                                <font style="vertical-align: inherit;">
                                                    <font
                                                        style="vertical-align: inherit;font-size: 14px;color:#000;font-weight: 400;">
                                                        Status :</font>
                                                </font><br>
                                            </td>
                                            <td
                                                style="padding:5px;vertical-align:top;text-align:left;padding-bottom:20px">
                                                <font style="vertical-align: inherit;margin-bottom:25px;">
                                                    <font
                                                        style="vertical-align: inherit;font-size:14px;color:#7367F0;font-weight:600;line-height: 35px; ">
                                                        &nbsp;</font>
                                                </font><br>
                                                <font style="vertical-align: inherit;">
                                                    <font
                                                        style="vertical-align: inherit;font-size: 14px;color:#000;font-weight: 400;">
                                                        <?php echo e($order->id); ?> </font>
                                                </font><br>
                                                <font style="vertical-align: inherit;">
                                                    <font
                                                        style="vertical-align: inherit;font-size: 14px;color:#2E7D32;font-weight: 400;">
                                                        <?php if($order->payment_status == 'paid'): ?>
                                                        <span class="text-success">Paid</span>
                                                        <?php else: ?>
                                                        <span class="text-danger">Not Paid</span>
                                                        <?php endif; ?>
                                                    </font>
                                                </font><br>
                                                <font style="vertical-align: inherit;">
                                                    <?php if($order->status == 'pending'): ?>
                                                    <span class="text-danger">Pending</span>
                                                    <?php elseif($order->status == 'shipped'): ?>
                                                    <span class="text-info">Shipped</span>
                                                    <?php elseif($order->status == 'delivered'): ?>
                                                    <span class="text-success">Delivered</span>
                                                    <?php else: ?>
                                                    <span class="text-danger">Canceled</span>
                                                    <?php endif; ?>  
                                                    
                                                </font><br>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </td>
                        </tr>
                        <tr class="heading " style="background: #F3F2F7;">
                            <td
                                style="padding: 5px;vertical-align: middle;font-weight: 600;color: #5E5873;font-size: 14px;padding: 10px; ">
                                Product Name
                            </td>
                            <td
                                style="padding: 5px;vertical-align: middle;font-weight: 600;color: #5E5873;font-size: 14px;padding: 10px; ">
                                QTY
                            </td>
                            <td
                                style="padding: 5px;vertical-align: middle;font-weight: 600;color: #5E5873;font-size: 14px;padding: 10px; ">
                                Price
                            </td>
                            
                            <td
                                style="padding: 5px;vertical-align: middle;font-weight: 600;color: #5E5873;font-size: 14px;padding: 10px; ">
                                Total
                            </td>
                        </tr>
                        <?php $__empty_1 = true; $__currentLoopData = $orderItems; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                        <tr class="details" style="border-bottom:1px solid #E9ECEF ;">
                            <td
                                style="padding: 10px;vertical-align: top; display: flex;align-items: center;">
                                    <?php echo e($item->name); ?>

                                </td>
                            <td style="padding: 10px;vertical-align: top; ">
                                <?php echo e($item->qty); ?>

                            </td>
                            <td style="padding: 10px;vertical-align: top; ">
                                <?php echo e($item->price); ?>      
                            </td>
                            <td style="padding: 10px;vertical-align: top; ">
                                <?php echo e($item->total); ?>          
                            </td>
                        </tr>
                            
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                            <tr>
                                <td colspan="4" style="padding: 10px; vertical-align: top; ">
                                    No products found for this order.
                                </td>
                            </tr>
                        <?php endif; ?>
                    </tbody>
                </table>
                <?php if($order->notes): ?>
                <br>
                <p><span class="fw-bold">Notes:</span> <?php echo e($order->notes); ?></p>
                <?php endif; ?>
            </div>
            <div class="container">
                <div class="row">
                    <!-- النموذج الأول -->
                    <div class="col-lg-6 col-md-12 mb-3">
                        <form method="post" action="<?php echo e(route('order.updatestatus', $order->id)); ?>" name="changeOrderStatusForm" id="changeOrderStatusForm">
                            <?php echo csrf_field(); ?>
                            <?php echo method_field('PUT'); ?>
                            <div class="form-group">
                                <label>Shipped time</label>
                            
                                    <div class="input-groupicon">
                                        <input type="text" name="shipped_date" placeholder="DD-MM-YYYY" value="<?php echo e(old('shipped_date', $order->shipped_date)); ?>" class="datetimepicker">
                                        <div class="addonset">
                                            <img src="<?php echo e(asset('admin/assets/img/icons/calendars.svg')); ?>" alt="img">
                                        </div>
                                    </div>
                                
                            </div>
                            <div class="form-group">
                                <label>Status</label>
                                <select name="status" id="status" class="select">
                                    <option value="pending" <?php echo e(($order->status == 'pending') ? 'selected' : ''); ?>>pending</option>
                                    <option value="shipped" <?php echo e(($order->status == 'shipped') ? 'selected' : ''); ?>>shipped</option>
                                    <option value="delivered" <?php echo e(($order->status == 'delivered') ? 'selected' : ''); ?>>delivered</option>
                                    <option value="cancelled" <?php echo e(($order->status == 'cancelled') ? 'selected' : ''); ?>>cancelled</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label>Payment Status</label>
                                <select name="payment_status" id="payment_status" class="select">
                                    <option value="paid" <?php echo e(($order->payment_status == 'paid') ? 'selected' : ''); ?>>Paid</option>
                                    <option value="not paid" <?php echo e(($order->payment_status == 'not paid') ? 'selected' : ''); ?>>Not Paid</option>
                                </select>
                            </div>
                            <button type="submit" class="btn btn-primary btn-sm me-2">Update</button>
                            <a href="<?php echo e(route('orders.index')); ?>" class="btn btn-secondary btn-sm">Cancel</a>
                        </form>
                    </div>
            
                    <!-- النموذج الثاني -->
                    <div class="col-lg-6 col-md-12 mb-3">
                        <form action="" method="post" name="sendInvoiceEmail" id="sendInvoiceEmail" >
                            <div class="form-group">
                                <label>Send invoice Email</label>
                                <select name="userType" id="userType" class="select">
                                    <option value="customer" <?php echo e(($order->status == 'pending') ? 'selected' : ''); ?>>Customer</option>
                                    <option value="admin" <?php echo e(($order->status == 'shipped') ? 'selected' : ''); ?>>Admin</option>
                                </select>
                            </div>
                            <button type="submit" class="btn btn-primary btn-sm me-2">Send</button>
                            <a href="<?php echo e(route('orders.index')); ?>" class="btn btn-secondary btn-sm">Cancel</a>
                        </form>
                    </div>
                </div>
            </div>
            
                    <div class="row">
                        <div class="col-lg-6 ms-auto">
                            <div class="total-order w-100 max-widthauto m-auto mb-4">
                                <ul>
                                    <li>
                                        <h4>Subtotal</h4>
                                        <h5><?php echo e($order->subtotal); ?> EGP</h5>
                                    </li>
                                    <li>
                                        <h4>Discount <?php echo e((!empty($order->code)) ? '('.$order->coupon_code.')' : ''); ?></h4>
                                        <h5><?php echo e($order->discount); ?> EGP</h5>
                                    </li>
                                    <li>
                                        <h4>Shipping</h4>
                                        <h5><?php echo e($order->shipping); ?> EGP</h5>
                                    </li>
                                    <li class="total">
                                        <h4>Grand Total</h4>
                                        <h5><?php echo e($order->grand_total); ?> EGP</h5>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
            
                    
                </div>
        </div>
    </div>
</div>

<script>
    $('#sendInvoiceEmail').submit(function(event) {
        event.preventDefault();

        var formData = $(this).serializeArray();

        $.ajax({
            url: '<?php echo e(route("order.sendInvoiceEmail",$order->id)); ?>',
            method: 'post',
            headers: {
                'X-CSRF-TOKEN': '<?php echo e(csrf_token()); ?>'
            },
            data: formData,
            dataType: 'json',
            success: function(response) {
                window.location.href = '<?php echo e(route('order.detail', $order->id)); ?>';
            }
        });
    });

    $('#changeOrderStatusForm').submit(function(event) {
        event.preventDefault();

        var formData = $(this).serializeArray();
        var url = $(this).attr('action');

        $.ajax({
            url: url,
            method: 'PUT',
            headers: {
                'X-CSRF-TOKEN': '<?php echo e(csrf_token()); ?>'
            },
            data: formData,
            dataType: 'json',
            success: function(response) {
                if (response.status) {
                    window.location.href = '<?php echo e(route('order.detail', $order->id)); ?>';
                } else {
                    alert(response.message);
                }
            },
            error: function(xhr, status, error) {
                console.error('Error:', xhr.responseText);
            }
        });
    });
</script>

<?php $__env->stopSection(); ?>
<?php echo $__env->make('admin.layouts.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\laragon\www\ecommerce-partscars\resources\views/admin/orders/detail.blade.php ENDPATH**/ ?>