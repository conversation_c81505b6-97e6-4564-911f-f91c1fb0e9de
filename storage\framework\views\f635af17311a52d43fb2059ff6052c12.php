
<?php $__env->startSection('TitlePage', 'users'); ?>
<?php $__env->startSection('content'); ?>
<div class="content">
    <div class="page-header">
        <div class="page-title">
            <h4>User List</h4>
            <h6>Manage your User</h6>
        </div>
        <div class="page-btn">
            <a href="<?php echo e(route('users.create')); ?>" class="btn btn-added"><img src="<?php echo e(asset('admin/assets/img/icons/plus.svg')); ?>" alt="img"
                    class="me-2">Add User</a>
        </div>
    </div>

    <div class="card">
        <div class="card-body">
            <div class="table-top">
                <div class="search-set">
                    <div class="search-input">
                        <a class="btn btn-searchset"><img src="<?php echo e(asset('admin/assets/img/icons/search-white.svg')); ?>"
                                alt="img"></a>
                    </div>
                </div>
                <div class="wordset">
                    <ul>
                        <li>
                            <a data-bs-toggle="tooltip" data-bs-placement="top" title="pdf"><img
                                    src="<?php echo e(asset('admin/assets/img/icons/pdf.svg')); ?>" alt="img"></a>
                        </li>
                        <li>
                            <a data-bs-toggle="tooltip" data-bs-placement="top" title="excel"><img
                                    src="<?php echo e(asset('admin/assets/img/icons/excel.svg')); ?>" alt="img"></a>
                        </li>
                        <li>
                            <a data-bs-toggle="tooltip" data-bs-placement="top" title="print"><img
                                    src="<?php echo e(asset('admin/assets/img/icons/printer.svg')); ?>" alt="img"></a>
                        </li>
                    </ul>
                </div>
            </div>

            <div class="table-responsive">
                <table class="table  datanew">
                    <thead>
                        <tr>
                            <th>
                                <label class="checkboxs">
                                    <input type="checkbox">
                                    <span class="checkmarks"></span>
                                </label>
                            </th>
                            <th>#</th>
                            <th>Profile</th>
                            <th>Name</th>
                            <th>Phone</th>
                            <th>Email</th>
                            <th>Region</th>
                            
                            <th>Status</th>
                            <th>Action</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php $__empty_1 = true; $__currentLoopData = $users; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $user): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                        <tr>
                                
                                
                            <td>
                                <label class="checkboxs">
                                    <input type="checkbox">
                                    <span class="checkmarks"></span>
                                </label>
                            </td>
                            <td><?php echo e($user->id); ?></td>
                            <td class="productimgname">
                                <a href="javascript:void(0);" class="product-img">
                                    <img src="assets/img/customer/customer1.jpg" alt="product">
                                </a>
                            </td>
                            <td><?php echo e($user->name); ?></td>
                            <td><?php echo e($user->phone); ?></td>
                            <td><?php echo e($user->email); ?></td>
                            <td><?php echo e($user->region); ?> </td>
                            <td>
                                <div class="status-toggle d-flex justify-content-between align-items-center">
                                    <input type="checkbox" id="user<?php echo e($user->id); ?>" class="check" <?php echo e($user->status ? 'checked' : ''); ?> disabled>
                                    <label for="user<?php echo e($user->id); ?>" class="checktoggle">checkbox</label>
                                </div>
                            </td>
                            <td>
                                <a class="me-3" href="<?php echo e(route('users.edit',$user->id)); ?>">
                                    <img src="<?php echo e(asset('admin/assets/img/icons/edit.svg')); ?>" alt="img">
                                </a>
                                <a class="me-3" href="javascript:void(0);" onclick="deleteRecord(<?php echo e($user->id); ?>)">
                                    <img src="<?php echo e(asset('admin/assets/img/icons/delete.svg')); ?>" alt="img">
                                </a>
                            </td>
                        </tr>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                        <tr>
                            <td colspan="9" class="text-center">No Users Yet!</td>
                        </tr>
                        <?php endif; ?>
                        
                    </tbody>
                </table>
            </div>
        </div>
    </div>

</div>

<script>
    function deleteRecord(id) {
        if(confirm("Are you sure you want to delete this record?")) {
            var url = '<?php echo e(route('users.destroy', 'id')); ?>';
            var newUrl = url.replace("id", id);

            $.ajax({
                headers: {
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                },
                url: newUrl,
                type: 'DELETE',
                dataType: 'json',
                success: function(response) {
                    if(response.status === true) {
                        window.location.href = "<?php echo e(route('users.index')); ?>";
                    }
                },
                error: function(xhr, status, error) {
                    alert("An error occurred while deleting the record. Please try again.");
                }
            });
        }
    }
</script>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('admin.layouts.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\laragon\www\ecommerce-partscars\resources\views/admin/users/index.blade.php ENDPATH**/ ?>