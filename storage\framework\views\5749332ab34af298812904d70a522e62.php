<?php $__env->startSection('TitlePage' , 'Cart'); ?>
<?php $__env->startSection('content'); ?>


    <!-- Breadcrumb Start -->
    <div class="container-fluid">
        <div class="row px-xl-5">
            <div class="col-12">
                <nav class="breadcrumb bg-light mb-30">
                    <a class="breadcrumb-item text-dark" href="#"><?php echo e(__('breadcrumb.home')); ?></a>
                    <a class="breadcrumb-item text-dark" href="#"><?php echo e(__('breadcrumb.shop')); ?></a>
                    <span class="breadcrumb-item active"><?php echo e(__('breadcrumb.cart')); ?></span>
                </nav>
            </div>
        </div>
    </div>
    <!-- Breadcrumb End -->


    <!-- Cart Start -->
    <div class="container-fluid">
        <div class="row px-xl-5">
            <div class="col-lg-8 table-responsive mb-5">
                <table class="table table-light table-borderless table-hover text-center mb-0">
                    <thead class="thead-dark">
                        <tr>
                            <th><?php echo e(__('cart.product')); ?></th>
                            <th><?php echo e(__('cart.price')); ?></th>
                            <th><?php echo e(__('cart.quantity')); ?></th>
                            <th><?php echo e(__('cart.total')); ?></th>
                            <th><?php echo e(__('cart.remove')); ?></th>
                        </tr>
                    </thead>
                    <tbody class="align-middle">
                        <?php $__empty_1 = true; $__currentLoopData = $cart_products; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $product): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                        <tr>
                            <td class="text-start">
                                <?php if($product->product->images->isNotEmpty()): ?>
                                    <img src="<?php echo e(Storage::url($product->product->images->first()->image_path)); ?>" alt="<?php echo e($product->product->name); ?>" class="pe-1" style="width: 50px; height: 50px;">
                                <?php else: ?>
                                    <img src="<?php echo e(asset('admin/assets/img/product/noimage.png')); ?>" alt="No Image" style="width: 50px;">
                                <?php endif; ?>
                                <?php echo e($product->product->name); ?>

                            </td>
                            <td class="align-middle"><?php echo e($product->product->selling_price); ?> <?php echo e(__('product.egp')); ?></td>
                            <td class="align-middle">
                                <div class="input-group qty mx-auto" style="width: 100px;">
                                    <button class="btn btn-sm btn-primary btn-minus" data-id="<?php echo e($product->product_id ?? $product->id); ?>">
                                        <i class="fa fa-minus"></i>
                                    </button>
                                    <input id="qty_<?php echo e($product->product_id ?? $product->id); ?>"
                                            min="1"
                                            name="qty[<?php echo e($product->product_id ?? $product->id); ?>]"
                                            type="text"
                                            class="form-control form-control-sm bg-secondary border-0 text-center qty-input"
                                            value="<?php echo e($product->qty); ?>"
                                            data-id="<?php echo e($product->product_id ?? $product->id); ?>" disabled>
                                    <button class="btn btn-sm btn-primary btn-plus" data-id="<?php echo e($product->product_id ?? $product->id); ?>">
                                        <i class="fa fa-plus"></i>
                                    </button>
                                </div>
                            </td>
                            <td class="align-middle" id="total-price-<?php echo e($product->id); ?>"><?php echo e($product->product->selling_price * $product->qty); ?> <?php echo e(__('product.egp')); ?></td>

                            <td class="align-middle">
                                <button type="button" class="btn btn-sm btn-danger" data-bs-toggle="modal" data-bs-target="#deletecartModal-<?php echo e($product->id); ?>">
                                    <i class="fa fa-times"></i>
                                </button>
                            </td>
                        </tr>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                        <tr>
                            <td colspan="5" class="text-center"><?php echo e(__('cart.empty_cart')); ?></td>
                        </tr>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
            <div class="col-lg-4">

                <h5 class="section-title position-relative text-uppercase mb-3">
                    <span class="bg-secondary pr-3"><?php echo e(__('cart.cart_summary')); ?></span>
                </h5>
                <div class="bg-light p-30 mb-5">
                    <div class="border-bottom pb-2">
                        <div class="d-flex justify-content-between mb-3">
                            <h6><?php echo e(__('cart.subtotal')); ?></h6>
                            <h6><?php echo e($total_price); ?> <?php echo e(__('product.egp')); ?></h6>
                        </div>
                    </div>
                    <div class="pt-2">
                        <a href="<?php echo e(route('checkout.index')); ?>" class="btn btn-block btn-primary font-weight-bold my-3 py-3"><?php echo e(__('cart.proceed_to_checkout')); ?></a>
                    </div>
                    <div class="d-flex justify-content-between mt-2">
                        <form action="<?php echo e(route('cart.update')); ?>" method="POST" class="d-inline">
                            <?php echo csrf_field(); ?>
                            <?php echo method_field('PUT'); ?> <!-- أو <?php echo method_field('PATCH'); ?> -->
                            <button type="submit" class="btn btn-secondary font-weight-bold py-3 flex-grow-1 mr-2"><?php echo e(__('cart.update_cart')); ?></button>
                        </form>
                        <a href="<?php echo e(route('website.shop')); ?>" class="btn btn-primary font-weight-bold py-3 flex-grow-1 ml-2"><?php echo e(__('cart.continue_shopping')); ?></a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Cart End -->

    <!-- Delete product from cart Modal -->
    <?php $__currentLoopData = $cart_products; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $product): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
    <div class="modal fade" id="deletecartModal-<?php echo e($product->id); ?>" tabindex="-1" aria-labelledby="deletecartModalLabel-<?php echo e($product->id); ?>" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h1 class="modal-title fs-5" id="deletecartModalLabel-<?php echo e($product->id); ?>"><?php echo e(__('cart.remove_product')); ?></h1>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <form action="<?php echo e(route('cart.destroy', $product->id)); ?>" method="post">
                    <?php echo method_field('DELETE'); ?>
                    <?php echo csrf_field(); ?>
                    <div class="modal-body">
                        <?php echo e(__('cart.delete_from_cart', ['product' => $product->product->name])); ?>

                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal"><?php echo e(__('cart.close')); ?></button>
                        <button type="submit" class="btn btn-danger"><?php echo e(__('cart.remove_product')); ?></button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

    <?php $__env->startSection('customjs'); ?>
    <script>

    document.querySelectorAll('.btn-plus, .btn-minus').forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();
            const productId = this.getAttribute('data-id');
            const inputField = document.querySelector(`#qty_${productId}`);
            let quantity = parseInt(inputField.value);

            if (this.classList.contains('btn-plus')) {
                quantity += 1;
            } else if (this.classList.contains('btn-minus') && quantity > 1) {
                quantity -= 1;
            }

            inputField.value = quantity;

            // إرسال طلب AJAX لتحديث الكمية
            fetch("<?php echo e(route('cart.update')); ?>", {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': '<?php echo e(csrf_token()); ?>'
                },
                body: JSON.stringify({
                    id: productId, // تأكد من أن productId صحيح
                    qty: quantity
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    console.log('Cart updated successfully');
                    // تحديث الواجهة بناءً على الرد (مثل تحديث المجموع الكلي)
                } else {
                    console.error('Failed to update cart');
                }
            })
            .catch(error => console.error('Error:', error));
        });
    });

    </script>
    <?php $__env->stopSection(); ?>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('website.layouts.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\laragon\www\ecommerce-partscars\resources\views/website/cart/cart.blade.php ENDPATH**/ ?>