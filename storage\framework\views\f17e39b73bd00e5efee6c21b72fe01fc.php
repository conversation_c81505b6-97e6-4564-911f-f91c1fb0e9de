<?php $__env->startSection('TitlePage', 'Products list'); ?>
<?php $__env->startSection('content'); ?>
<div class="content">
    <div class="page-header">
        <div class="page-title">
            <h4>Product List</h4>
            <h6>Manage your products</h6>
        </div>
        <div class="page-btn">
            <a href="<?php echo e(route('products.create')); ?>" class="btn btn-added">
                <img src="<?php echo e(asset('admin/assets/img/icons/plus.svg')); ?>" alt="img" class="me-1">Add New Product
            </a>
        </div>
    </div>

    <div class="card">
        <div class="card-body">
            <div class="table-top">
                <div class="search-set">
                    <div class="search-input">
                        <a class="btn btn-searchset"><img src="<?php echo e(asset('admin/assets/img/icons/search-white.svg')); ?>"
                                alt="img"></a>
                    </div>
                </div>
                <div class="wordset">
                    <ul>
                        <li>
                            <a data-bs-toggle="tooltip" data-bs-placement="top" title="pdf"><img
                                    src="<?php echo e(asset('admin/assets/img/icons/pdf.svg')); ?>" alt="img"></a>
                        </li>
                        <li>
                            <a data-bs-toggle="tooltip" data-bs-placement="top" title="excel"><img
                                    src="<?php echo e(asset('admin/assets/img/icons/excel.svg')); ?>" alt="img"></a>
                        </li>
                        <li>
                            <a data-bs-toggle="tooltip" data-bs-placement="top" title="print"><img
                                    src="<?php echo e(asset('admin/assets/img/icons/printer.svg')); ?>" alt="img"></a>
                        </li>
                    </ul>
                </div>
            </div>


            <div class="table-responsive">
                <table class="table  datanew">
                    <thead>
                        <tr>
                            <th>
                                <label class="checkboxs">
                                    <input type="checkbox" id="select-all">
                                    <span class="checkmarks"></span>
                                </label>
                            </th>
                            <th>Product Name</th>
                            <th>code</th>
                            <th>Category </th>
                            <th>status</th>
                            <th>price</th>
                            <th>Unit</th>
                            <th>Qty</th>
                            <th>Created By</th>
                            <th>Action</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php $__empty_1 = true; $__currentLoopData = $products; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $product): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>


                        <tr>
                            <td>
                                <label class="checkboxs">
                                    <input type="checkbox">
                                    <span class="checkmarks"></span>
                                </label>
                            </td>
                            <td class="productimgname">
                                <a href="javascript:void(0);" class="product-img">
                                    <?php
                                    $image = $product->images->first();
                                    ?>
                                    <?php if($image && $image->image_path): ?>
                                        <img src="<?php echo e(Storage::url($image->image_path)); ?>" alt="<?php echo e($product->name); ?>" width="50" class="img-thumbnail">
                                    <?php else: ?>
                                        <img width="50" src="<?php echo e(asset('admin/assets/img/product/noimage.png')); ?>" alt="<?php echo e(($product->name)); ?>"
                                        class="img-thumbnail">
                                    <?php endif; ?>
                                </a>
                                <a href="javascript:void(0);"><?php echo e($product->name); ?></a>
                            </td>
                            <td>Ft-2<?php echo e($product->id); ?>01</td>
                            <td><?php echo e($product->category->name); ?></td>
                            <td>
                                <?php if($product->status == 1): ?>
                                <span class="badge bg-success">show</span>
                                <?php else: ?>
                                <span class="badge bg-danger">don't show</span>
                                <?php endif; ?>
                            </td>
                            <td><?php echo e($product->price); ?></td>
                            <td>N/D</td>
                            <td><?php echo e($product->qty); ?></td>
                            <td>N/D</td>
                            <td class="text-end">
                                <a class="me-3" href="<?php echo e(route('products.show',$product->id)); ?>">
                                    <img src="<?php echo e(asset('admin/assets/img/icons/eye.svg')); ?>" alt="img">
                                </a>
                                <a class="me-3" href="<?php echo e(route('products.edit',$product->id)); ?>">
                                    <img src="<?php echo e(asset('admin/assets/img/icons/edit.svg')); ?>" alt="img">
                                </a>
                                <?php echo $__env->make('admin.products.delete_modal',['type'=>'product','data'=>$product,'routes'=>'products.destroy'], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                            </td>
                        </tr>

                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('admin.layouts.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\laragon\www\ecommerce-partscars\resources\views/admin/products/index.blade.php ENDPATH**/ ?>