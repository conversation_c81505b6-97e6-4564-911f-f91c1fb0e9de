<?php $__env->startSection('TitlePage', 'General Settings'); ?>
<?php $__env->startSection('content'); ?>
<div class="content">
    <div class="page-header">
        <div class="page-title">
            <h4>General Setting</h4>
            <h6>Manage General Setting</h6>
        </div>
    </div>

    <div class="card">
        <div class="card-body">
            <form action="<?php echo e(route('settings.update')); ?>" method="POST" enctype="multipart/form-data">
                <?php echo csrf_field(); ?>
                <?php echo method_field('PUT'); ?>
                <div class="row">
                    <div class="col-lg-7 col-sm-6 col-12">
                        <div class="form-group">
                            <label>Site Name <span class="manitory">*</span></label>
                            <input type="text" name="site_name" value="<?php echo e(old('site_name', $settings->site_name ?? '')); ?>" placeholder="Enter Site Name" class="form-control">
                            <input type="text" name="site_name_ar" value="<?php echo e(old('site_name_ar', $settings->getTranslation('site_name', 'ar') ?? '')); ?>" placeholder="ادخل اسم الموقع بالعربي" class="form-control mt-2">
                            <?php $__errorArgs = ['site_name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <span class="text-danger"><?php echo e($message); ?></span>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>
                    </div>
                    <div class="col-lg-5 col-sm-6 col-12">
                        <div class="form-group">
                            <label>Email<span class="manitory">*</span></label>
                            <input type="text" name="email" value="<?php echo e(old('email', $settings->email ?? '')); ?>" placeholder="Enter email" class="form-control">
                        </div>
                    </div>

                    <div class="col-lg-7">
                        <div class="form-group">
                            <label>Site Image</label>
                            <div class="image-upload">
                                <input type="file" name="site_image" id="images" class="form-control">
                                <div class="image-uploads">
                                    <img src="<?php echo e(asset('admin/assets/img/icons/upload.svg')); ?>" alt="img">
                                    <h4>Drag and drop a file to upload</h4>
                                </div>
                                <?php $__errorArgs = ['site_image'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <span class="text-danger"><?php echo e($message); ?></span>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                        </div>
                    </div>

                    <div class="col-lg-5">
                        <div class="form-group">
                            <label>Preview Image</label>
                            <?php if($settings->site_image ?? false): ?>
                                <img src="<?php echo e(asset('storage/images/' . $settings->site_image)); ?>" alt="Site Image" class="img-thumbnail mt-2" width="150">
                            <?php endif; ?>
                            <div class="preview-images rounded d-inline" ></div>
                        </div>
                    </div>

                    <div class="col-lg-12 col-sm-6 col-12">
                        <div class="form-group">
                            <label>Company Description<span class="manitory">*</span></label>
                            <input type="text" name="company_description" value="<?php echo e(old('company_description', $settings->company_description ?? '')); ?>" placeholder="Enter Company Description" class="form-control">
                            <input type="text" name="company_description_ar" value="<?php echo e(old('company_description_ar', $settings->getTranslation('company_description', 'ar') ?? '')); ?>" placeholder="ادخل وصف الشركة بالعربي" class="form-control mt-2">
                        </div>
                    </div>
                    <div class="col-lg-6 col-sm-6 col-12">
                        <div class="form-group">
                            <label>Phone Number</label>
                            <input type="text" name="phone_number" value="<?php echo e(old('phone_number', $settings->phone_number ?? '')); ?>" placeholder="Enter Phone Number">
                        </div>
                    </div>
                    <div class="col-lg-6 col-sm-6 col-12">
                        <div class="form-group">
                            <label>Hotline<span class="manitory">*</span></label>
                            <input type="text" name="hotline" value="<?php echo e(old('hotline', $settings->hotline ?? '')); ?>" placeholder="Enter hotline">
                        </div>
                    </div>

                    <div class="col-lg-12 col-sm-6 col-12">
                        <div class="form-group">
                            <label>Address <span class="manitory">*</span></label>
                            <input type="text" name="address" value="<?php echo e(old('address', $settings->address ?? '')); ?>" placeholder="Enter Address" class="form-control">
                            <input type="text" name="address_ar" value="<?php echo e(old('address_ar', $settings->getTranslation('address', 'ar') ?? '')); ?>" placeholder="ادخل العنوان بالعربي" class="form-control mt-2">
                        </div>
                    </div>

                    <div class="col-lg-6 col-sm-12">
                        <div class="form-group">
                            <label>Map Link<span class="manitory">*</span></label>
                            <input type="text" name="map_link" value="<?php echo e(old('map_link', $settings->map_link ?? '')); ?>" placeholder="Enter Map Link">
                        </div>
                    </div>
                    <div class="col-lg-6 col-sm-12">
                        <div class="form-group">
                            <label>Facebook Link<span class="manitory">*</span></label>
                            <input type="text" name="facebook_link" value="<?php echo e(old('facebook_link', $settings->facebook_link ?? '')); ?>" placeholder="Enter Facebook Link">
                        </div>
                    </div>

                    <div class="col-lg-6 col-sm-12">
                        <div class="form-group">
                            <label>Whatsapp Number<span class="manitory">*</span></label>
                            <input type="text" name="whatsapp_number" value="<?php echo e(old('whatsapp_number', $settings->whatsapp_number ?? '')); ?>" placeholder="Enter Whatsapp Link">
                        </div>
                    </div>
                    <div class="col-lg-6 col-sm-12">
                        <div class="form-group">
                            <label>Twitter Link<span class="manitory">*</span></label>
                            <input type="text" name="twitter_link" value="<?php echo e(old('twitter_link', $settings->twitter_link ?? '')); ?>" placeholder="Enter Twitter Link">
                        </div>
                    </div>

                    <div class="col-lg-6 col-sm-12">
                        <div class="form-group">
                            <label>Linkedin Link<span class="manitory">*</span></label>
                            <input type="text" name="linkedin_link" value="<?php echo e(old('linkedin_link', $settings->linkedin_link ?? '')); ?>" placeholder="Enter Linkedin Link">
                        </div>
                    </div>
                    <div class="col-lg-6 col-sm-12">
                        <div class="form-group">
                            <label>Working Hours<span class="manitory">*</span></label>
                            <input type="text" name="working_hours" value="<?php echo e(old('working_hours', $settings->working_hours ?? '')); ?>" placeholder="Enter Working Hours ( from 10 pm to 10 am )">
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-lg-12">
                            <button type="submit" class="btn btn-submit me-2">Save & update</button>
                            <a href="<?php echo e(route('admin.dashboard')); ?>" class="btn btn-cancel">Cancel</a>
                        </div>
                    </div>
                </div>
            </form>

        </div>
    </div>

</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('admin.layouts.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\laragon\www\ecommerce-partscars\resources\views/admin/settings/generalsetting.blade.php ENDPATH**/ ?>