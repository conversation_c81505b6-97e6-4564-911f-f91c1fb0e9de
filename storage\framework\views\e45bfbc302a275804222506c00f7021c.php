<?php $__env->startSection('TitlePage', 'categories'); ?>
<?php $__env->startSection('content'); ?>
<div class="content">
    <div class="page-header">
        <div class="page-title">
            <h4>Product Category list</h4>
            <h6>View/Search product Category</h6>
        </div>
        <div class="page-btn">
            <a href="<?php echo e(route('categories.create')); ?>" class="btn btn-added">
                <img src="<?php echo e(asset('admin/assets/img/icons/plus.svg')); ?>" class="me-1" alt="img">Add Category
            </a>
        </div>
    </div>

    <div class="card">
        <div class="card-body">
            <div class="table-top">
                <div class="search-set">
                    <div class="search-input">
                        <a class="btn btn-searchset"><img src="<?php echo e(asset('admin/assets/img/icons/search-white.svg')); ?>"
                                alt="img"></a>
                    </div>
                </div>
                <div class="wordset">
                    <ul>
                        <li>
                            <a data-bs-toggle="tooltip" data-bs-placement="top" title="pdf"><img
                                    src="<?php echo e(asset('admin/assets/img/icons/pdf.svg')); ?>" alt="img"></a>
                        </li>
                        <li>
                            <a data-bs-toggle="tooltip" data-bs-placement="top" title="excel"><img
                                    src="<?php echo e(asset('admin/assets/img/icons/excel.svg')); ?>" alt="img"></a>
                        </li>
                        <li>
                            <a data-bs-toggle="tooltip" data-bs-placement="top" title="print"><img
                                    src="<?php echo e(asset('admin/assets/img/icons/printer.svg')); ?>" alt="img"></a>
                        </li>
                    </ul>
                </div>
            </div>



            <div class="table-responsive">
                <table class="table ">
                    <thead>
                        <tr>
                            <th>#</th>
                            <th>Category name</th>
                            <th>is_showing</th>
                            <th>is_popular</th>
                            <th>slug</th>
                            <th>Action</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php $__empty_1 = true; $__currentLoopData = $categories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                        <tr>

                            <th scope="row"><?php echo e($loop->iteration); ?></th>
                            <td class="productimgname">
                                <a class="product-img">
                                    <img width="50" src="<?php echo e(Storage::url($category->image)); ?>" alt="<?php echo e($category->name); ?>"
                                    class="img-thumbnail">
                                </a>
                                <a><?php echo e($category->name); ?></a>
                            </td>
                            <td>
                                <?php if($category->is_showing == 1): ?>
                                <span class="badge bg-success">show</span>
                                <?php else: ?>
                                <span class="badge bg-danger">don't show</span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <?php if($category->is_popular == 1): ?>
                                <span class="badge bg-success">popular</span>
                                <?php else: ?>
                                <span class="badge bg-danger">don't popular</span>
                                <?php endif; ?>
                            </td>
                            <td><?php echo e($category->slug); ?></td>
                            <td>
                                <a class="me-3" href="<?php echo e(route('categories.show',$category->id)); ?>">
                                    <img src="<?php echo e(asset('admin/assets/img/icons/eye.svg')); ?>" alt="img">
                                </a>
                                <a class="me-3" href="<?php echo e(route('categories.edit',$category->id)); ?>">
                                    <img src="<?php echo e(asset('admin/assets/img/icons/edit.svg')); ?>" alt="img">
                                </a>

                                <?php echo $__env->make('admin.categories.delete_modal',['type'=>'category','data'=>$category,'routes'=>'categories.destroy'], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

                            </td>
                        </tr>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                        <tr>
                            <td colspan="6" class="text-center">No Products Yet!</td>
                        </tr>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('admin.layouts.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\laragon\www\ecommerce-partscars\resources\views/admin/categories/index.blade.php ENDPATH**/ ?>