<?php $__env->startSection('TitlePage', 'Orders List'); ?>
<?php $__env->startSection('content'); ?>
<div class="content">
    <div class="page-header">
        <div class="page-title">
            <h4>Orders List</h4>
            <h6>Manage your Order</h6>
        </div>
    </div>

    <div class="card">
        <div class="card-body">
            <div class="table-top">
                <div class="search-set">
                    <div class="search-input">
                        <a class="btn btn-searchset"><img src="<?php echo e(asset('admin/assets/img/icons/search-white.svg')); ?>"
                                alt="img"></a>
                    </div>
                </div>
                <div class="wordset">
                    <ul>
                        <li>
                            <a data-bs-toggle="tooltip" data-bs-placement="top" title="pdf"><img
                                    src="<?php echo e(asset('admin/assets/img/icons/pdf.svg')); ?>" alt="img"></a>
                        </li>
                        <li>
                            <a data-bs-toggle="tooltip" data-bs-placement="top" title="excel"><img
                                    src="<?php echo e(asset('admin/assets/img/icons/excel.svg')); ?>" alt="img"></a>
                        </li>
                        <li>
                            <a data-bs-toggle="tooltip" data-bs-placement="top" title="print"><img
                                    src="<?php echo e(asset('admin/assets/img/icons/printer.svg')); ?>" alt="img"></a>
                        </li>
                    </ul>
                </div>
            </div>

            <div class="card" id="filter_inputs">
                <div class="card-body pb-0">
                    <div class="row">
                        <div class="col-lg-3 col-sm-6 col-12">
                            <div class="form-group">
                                <input type="text" placeholder="Enter Name">
                            </div>
                        </div>
                        <div class="col-lg-3 col-sm-6 col-12">
                            <div class="form-group">
                                <input type="text" placeholder="Enter Reference No">
                            </div>
                        </div>
                        <div class="col-lg-3 col-sm-6 col-12">
                            <div class="form-group">
                                <select class="select">
                                    <option>Completed</option>
                                    <option>Paid</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-lg-3 col-sm-6 col-12">
                            <div class="form-group">
                                <a class="btn btn-filters ms-auto"><img
                                        src="assets/img/icons/search-whites.svg" alt="img"></a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="table-responsive">
                <table class="table datanew">
                    <thead>
                        <tr>
                            <th>
                                <label class="checkboxs">
                                    <input type="checkbox" id="select-all">
                                    <span class="checkmarks"></span>
                                </label>
                            </th>
                            <th>#</th>
                            <th>Customer Name</th>
                            <th>Date</th>
                            <th>Email</th>
                            <th>Phone</th>
                            <th>Status</th>
                            <th>Payment</th>
                            <th>Total</th>
                            <th>Shipped date</th>
                            <th class="text-center">Action</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php $__empty_1 = true; $__currentLoopData = $orders; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $order): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                        <tr>
                            <td>
                                <label class="checkboxs">
                                    <input type="checkbox">
                                    <span class="checkmarks"></span>
                                </label>
                            </td>
                            <td><a href="<?php echo e(route('order.detail', ['orderId' => $order->id])); ?>"><?php echo e($order->id); ?></a></td>
                            <td><a href="<?php echo e(route('order.detail', ['orderId' => $order->id])); ?>"><?php echo e($order->name); ?></a></td>
                            <td><?php echo e(\Carbon\Carbon::parse($order->created_at)->format('d M, Y - D')); ?></td>
                            <td><?php echo e($order->email); ?></td>
                            <td><?php echo e($order->mobile); ?></td>

                            <td>
                                <?php if($order->status == 'delivered'): ?>
                                <span class="badges bg-lightgreen">Delivered</span>
                                <?php elseif($order->status == 'shipped'): ?>
                                <span class="badges bg-lightyellow">Shipped</span>
                                <?php elseif($order->status == 'pending'): ?>
                                <span class="badges bg-primary">Pending</span>
                                <?php else: ?>
                                <span class="badges bg-lightred fw-bold">Cancelled</span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <?php if($order->payment_status == 'paid'): ?>
                                <span class="badges bg-lightgreen">Paid</span>
                                <?php else: ?>
                                <span class="badges bg-lightred">Not Paid</span>
                                <?php endif; ?>
                            </td>
                            <td><?php echo e($order->grand_total); ?> EGP</td>
                            <td><?php echo e($order->shipped_date ? $order->shipped_date : 'N/A'); ?></td>
                            <td class="text-center">
                                <a class="action-set" href="javascript:void(0);" data-bs-toggle="dropdown"
                                    aria-expanded="true">
                                    <i class="fa fa-ellipsis-v" aria-hidden="true"></i>
                                </a>
                                <ul class="dropdown-menu">
                                    <li>
                                        <a href="sales-details.html" class="dropdown-item"><img
                                                src="assets/img/icons/eye1.svg" class="me-2" alt="img">Sale
                                            Detail</a>
                                    </li>
                                    <li>
                                        <a href="edit-sales.html" class="dropdown-item"><img
                                                src="assets/img/icons/edit.svg" class="me-2" alt="img">Edit
                                            Sale</a>
                                    </li>
                                    <li>
                                        <a href="javascript:void(0);" class="dropdown-item"
                                            data-bs-toggle="modal" data-bs-target="#showpayment"><img
                                                src="assets/img/icons/dollar-square.svg" class="me-2"
                                                alt="img">Show Payments</a>
                                    </li>
                                    <li>
                                        <a href="javascript:void(0);" class="dropdown-item"
                                            data-bs-toggle="modal" data-bs-target="#createpayment"><img
                                                src="assets/img/icons/plus-circle.svg" class="me-2"
                                                alt="img">Create Payment</a>
                                    </li>
                                    <li>
                                        <a href="javascript:void(0);" class="dropdown-item"><img
                                                src="assets/img/icons/download.svg" class="me-2"
                                                alt="img">Download pdf</a>
                                    </li>
                                    <li>
                                        <a href="javascript:void(0);"
                                            class="dropdown-item confirm-text"><img
                                                src="assets/img/icons/delete1.svg" class="me-2"
                                                alt="img">Delete Sale</a>
                                    </li>
                                </ul>
                            </td>
                        </tr>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                            <tr>
                                <td colspan="11">No records found</td>
                            </tr>
                        <?php endif; ?>

                    </tbody>
                </table>
            </div>
        </div>
    </div>

</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('admin.layouts.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\laragon\www\ecommerce-partscars\resources\views/admin/orders/index.blade.php ENDPATH**/ ?>